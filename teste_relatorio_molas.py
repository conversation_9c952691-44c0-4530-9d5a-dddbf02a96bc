#!/usr/bin/env python
"""
Script para testar o relatório de Molas Mais Vendidas
"""
import os
import sys

# Adicionar o diretório do projeto ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configurar Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'controle_estoque.settings')

try:
    import django
    django.setup()
    print("Django configurado com sucesso!")
except Exception as e:
    print(f"Erro ao configurar Django: {e}")
    sys.exit(1)

from estoque.models import Mola, MovimentacaoEstoque, ItemPedido
from django.utils import timezone
from datetime import timedelta

def testar_dados_basicos():
    """Testa se há dados básicos no sistema"""
    print("=== TESTE DE DADOS BÁSICOS ===")
    print(f"Número de molas: {Mola.objects.count()}")
    print(f"Número de movimentações: {MovimentacaoEstoque.objects.count()}")
    print(f"Número de itens de pedido: {ItemPedido.objects.count()}")
    
    # Listar algumas molas
    molas = Mola.objects.all()[:5]
    print(f"\nPrimeiras 5 molas:")
    for mola in molas:
        print(f"  - {mola.codigo} ({mola.cliente})")
    
    return molas.count() > 0

def testar_metodo_mais_vendidas():
    """Testa o método mais_vendidas"""
    print("\n=== TESTE DO MÉTODO MAIS_VENDIDAS ===")

    try:
        # Testar com período 'mes'
        resultado = Mola.mais_vendidas(periodo='mes')
        print(f"Resultado para período 'mes': {len(resultado)} itens")

        if resultado:
            print("Primeiro item:")
            item = resultado[0]
            print(f"  - Código: {item.get('mola_codigo', 'N/A')}")
            print(f"  - Cliente: {item.get('mola_cliente', 'N/A')}")
            print(f"  - Total vendido: {item.get('total_vendido', 'N/A')}")
            print(f"  - Média mensal: {item.get('media_mensal', 'N/A')}")
            print(f"  - Variação %: {item.get('variacao_percentual', 'N/A')}")
            print(f"  - Variação média histórica: {item.get('variacao_media_historica', 'N/A')}")

            # Verificar se todos os campos necessários estão presentes
            campos_obrigatorios = ['mola_codigo', 'mola_cliente', 'total_vendido', 'media_mensal', 'variacao_percentual', 'variacao_media_historica']
            campos_faltando = [campo for campo in campos_obrigatorios if campo not in item]

            if campos_faltando:
                print(f"  ⚠️  CAMPOS FALTANDO: {campos_faltando}")
                return False
            else:
                print("  ✅ Todos os campos obrigatórios estão presentes")

        # Testar com todo o período
        resultado_todo = Mola.mais_vendidas()
        print(f"\nResultado para todo o período: {len(resultado_todo)} itens")

        return True

    except Exception as e:
        print(f"ERRO no método mais_vendidas: {e}")
        import traceback
        traceback.print_exc()
        return False

def testar_metodo_vendas_mensais():
    """Testa o método obter_vendas_mensais"""
    print("\n=== TESTE DO MÉTODO OBTER_VENDAS_MENSAIS ===")
    
    try:
        resultado = Mola.obter_vendas_mensais(periodo='mes')
        print(f"Resultado vendas mensais: {resultado}")
        return True
        
    except Exception as e:
        print(f"ERRO no método obter_vendas_mensais: {e}")
        import traceback
        traceback.print_exc()
        return False

def testar_movimentacoes_recentes():
    """Testa se há movimentações recentes"""
    print("\n=== TESTE DE MOVIMENTAÇÕES RECENTES ===")
    
    # Últimos 30 dias
    data_limite = timezone.now().date() - timedelta(days=30)
    movimentacoes_recentes = MovimentacaoEstoque.objects.filter(
        data__gte=data_limite,
        tipo='S'  # Saídas
    )
    
    print(f"Movimentações de saída nos últimos 30 dias: {movimentacoes_recentes.count()}")
    
    # Últimos 30 dias - itens de pedido
    itens_recentes = ItemPedido.objects.filter(
        atendido=True,
        pedido__data_pedido__gte=data_limite
    )
    
    print(f"Itens de pedido atendidos nos últimos 30 dias: {itens_recentes.count()}")
    
    return movimentacoes_recentes.count() > 0 or itens_recentes.count() > 0

def main():
    """Função principal"""
    print("INICIANDO TESTES DO RELATÓRIO DE MOLAS MAIS VENDIDAS")
    print("=" * 60)
    
    # Teste 1: Dados básicos
    tem_dados = testar_dados_basicos()
    
    if not tem_dados:
        print("\nAVISO: Não há dados suficientes para testar o relatório")
        return
    
    # Teste 2: Método mais_vendidas
    metodo_ok = testar_metodo_mais_vendidas()
    
    # Teste 3: Método vendas mensais
    vendas_ok = testar_metodo_vendas_mensais()
    
    # Teste 4: Movimentações recentes
    tem_movimentacoes = testar_movimentacoes_recentes()
    
    # Resumo
    print("\n" + "=" * 60)
    print("RESUMO DOS TESTES:")
    print(f"✓ Dados básicos: {'OK' if tem_dados else 'FALHA'}")
    print(f"✓ Método mais_vendidas: {'OK' if metodo_ok else 'FALHA'}")
    print(f"✓ Método vendas mensais: {'OK' if vendas_ok else 'FALHA'}")
    print(f"✓ Movimentações recentes: {'OK' if tem_movimentacoes else 'FALHA'}")
    
    if metodo_ok and vendas_ok:
        print("\n✅ RELATÓRIO PARECE ESTAR FUNCIONANDO CORRETAMENTE")
    else:
        print("\n❌ PROBLEMAS DETECTADOS NO RELATÓRIO")

if __name__ == "__main__":
    main()
