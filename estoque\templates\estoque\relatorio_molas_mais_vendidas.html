{% extends 'estoque/base.html' %}

{% block title %}Relatório de Molas Mais Vendidas - Controle de Estoque{% endblock %}

{% block extra_head %}
<style>
    .periodo-personalizado {
        transition: all 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="mt-4 mb-4">Relatório de Molas Mais Vendidas</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="post" class="row g-3">
                {% csrf_token %}

                <div class="col-md-3">
                    <label for="{{ form.periodo.id_for_label }}" class="form-label">Período</label>
                    {{ form.periodo.errors }}
                    <select name="{{ form.periodo.name }}" id="{{ form.periodo.id_for_label }}" class="form-control">
                        {% for value, text in form.fields.periodo.choices %}
                            <option value="{{ value }}" {% if form.periodo.value == value %}selected{% endif %}>{{ text }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="{{ form.cliente.id_for_label }}" class="form-label">Cliente</label>
                    {{ form.cliente.errors }}
                    <input type="text" name="{{ form.cliente.name }}" id="{{ form.cliente.id_for_label }}" class="form-control" value="{{ form.cliente.value|default:'' }}">
                </div>

                <div class="col-md-3">
                    <label for="{{ form.limite.id_for_label }}" class="form-label">Limite de Resultados</label>
                    {{ form.limite.errors }}
                    <input type="number" name="{{ form.limite.name }}" id="{{ form.limite.id_for_label }}" class="form-control" value="{{ form.limite.value|default:'10' }}" min="1" max="100">
                </div>

                <div class="col-md-3">
                    <label for="{{ form.formato_pdf.id_for_label }}" class="form-label">Formato do PDF</label>
                    {{ form.formato_pdf.errors }}
                    <select name="{{ form.formato_pdf.name }}" id="{{ form.formato_pdf.id_for_label }}" class="form-control">
                        {% for value, text in form.fields.formato_pdf.choices %}
                            <option value="{{ value }}" {% if form.formato_pdf.value == value %}selected{% endif %}>{{ text }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Campos de data para período personalizado -->
                <div class="col-md-6 mt-3 periodo-personalizado" style="display: {% if form.periodo.value == 'personalizado' %}block{% else %}none{% endif %};">
                    <label for="{{ form.data_inicial.id_for_label }}" class="form-label">Data Inicial</label>
                    {{ form.data_inicial.errors }}
                    {{ form.data_inicial }}
                </div>

                <div class="col-md-6 mt-3 periodo-personalizado" style="display: {% if form.periodo.value == 'personalizado' %}block{% else %}none{% endif %};">
                    <label for="{{ form.data_final.id_for_label }}" class="form-label">Data Final</label>
                    {{ form.data_final.errors }}
                    {{ form.data_final }}
                </div>

                <div class="col-md-12 mt-3 periodo-personalizado" style="display: {% if form.periodo.value == 'personalizado' %}block{% else %}none{% endif %};">
                    <label for="{{ form.subtitulo_personalizado.id_for_label }}" class="form-label">{{ form.subtitulo_personalizado.label }}</label>
                    {{ form.subtitulo_personalizado.errors }}
                    {{ form.subtitulo_personalizado }}
                </div>

                <!-- Campos para meses específicos -->
                <div class="col-md-12 mt-3 meses-especificos" style="display: {% if form.periodo.value == 'meses_especificos' %}block{% else %}none{% endif %};">
                    <div class="card">
                        <div class="card-header">
                            <label class="form-label">{{ form.meses_selecionados.label }}</label>
                            {{ form.meses_selecionados.errors }}
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% for value, text in form.meses_selecionados.field.choices %}
                                    <div class="col-md-3 mb-2">
                                        <div class="form-check">
                                            <input type="checkbox" name="{{ form.meses_selecionados.name }}" value="{{ value }}"
                                                id="id_{{ form.meses_selecionados.name }}_{{ forloop.counter0 }}"
                                                class="form-check-input"
                                                {% if value in form.meses_selecionados.value %}checked{% endif %}>
                                            <label class="form-check-label" for="id_{{ form.meses_selecionados.name }}_{{ forloop.counter0 }}">
                                                {{ text }}
                                            </label>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mt-3 meses-especificos" style="display: {% if form.periodo.value == 'meses_especificos' %}block{% else %}none{% endif %};">
                    <label for="{{ form.ano_selecionado.id_for_label }}" class="form-label">{{ form.ano_selecionado.label }}</label>
                    {{ form.ano_selecionado.errors }}
                    {{ form.ano_selecionado }}
                </div>

                <!-- Campos para comparação entre períodos -->
                <div class="col-md-12 mt-3 comparacao-periodos" style="display: {% if form.periodo.value == 'comparacao' %}block{% else %}none{% endif %};">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Configuração dos Períodos para Comparação</h6>
                        </div>
                        <div class="card-body">
                            <!-- Seletores intuitivos de mês/ano -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="usarSeletoresMesAno" checked>
                                        <label class="form-check-label" for="usarSeletoresMesAno">
                                            Usar seletores de mês/ano (recomendado)
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Seletores de mês/ano -->
                            <div class="row seletores-mes-ano">
                                <div class="col-md-6">
                                    <h6 class="text-primary">Período 1</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="{{ form.mes_comparacao1.id_for_label }}" class="form-label">{{ form.mes_comparacao1.label }}</label>
                                            {{ form.mes_comparacao1.errors }}
                                            {{ form.mes_comparacao1 }}
                                        </div>
                                        <div class="col-md-6">
                                            <label for="{{ form.ano_comparacao1.id_for_label }}" class="form-label">{{ form.ano_comparacao1.label }}</label>
                                            {{ form.ano_comparacao1.errors }}
                                            {{ form.ano_comparacao1 }}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary">Período 2</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="{{ form.mes_comparacao2.id_for_label }}" class="form-label">{{ form.mes_comparacao2.label }}</label>
                                            {{ form.mes_comparacao2.errors }}
                                            {{ form.mes_comparacao2 }}
                                        </div>
                                        <div class="col-md-6">
                                            <label for="{{ form.ano_comparacao2.id_for_label }}" class="form-label">{{ form.ano_comparacao2.label }}</label>
                                            {{ form.ano_comparacao2.errors }}
                                            {{ form.ano_comparacao2 }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Campos de data manual (ocultos por padrão) -->
                            <div class="row campos-data-manual" style="display: none;">
                                <div class="col-md-6">
                                    <h6 class="text-primary">Período 1</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="{{ form.data_inicial_comparacao1.id_for_label }}" class="form-label">{{ form.data_inicial_comparacao1.label }}</label>
                                            {{ form.data_inicial_comparacao1.errors }}
                                            {{ form.data_inicial_comparacao1 }}
                                        </div>
                                        <div class="col-md-6">
                                            <label for="{{ form.data_final_comparacao1.id_for_label }}" class="form-label">{{ form.data_final_comparacao1.label }}</label>
                                            {{ form.data_final_comparacao1.errors }}
                                            {{ form.data_final_comparacao1 }}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary">Período 2</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="{{ form.data_inicial_comparacao2.id_for_label }}" class="form-label">{{ form.data_inicial_comparacao2.label }}</label>
                                            {{ form.data_inicial_comparacao2.errors }}
                                            {{ form.data_inicial_comparacao2 }}
                                        </div>
                                        <div class="col-md-6">
                                            <label for="{{ form.data_final_comparacao2.id_for_label }}" class="form-label">{{ form.data_final_comparacao2.label }}</label>
                                            {{ form.data_final_comparacao2.errors }}
                                            {{ form.data_final_comparacao2 }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Campo de subtítulo personalizado para comparação -->
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <label for="{{ form.subtitulo_personalizado_comparacao.id_for_label }}" class="form-label">{{ form.subtitulo_personalizado_comparacao.label }}</label>
                                    {{ form.subtitulo_personalizado_comparacao.errors }}
                                    {{ form.subtitulo_personalizado_comparacao }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <div class="col-12 mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filtrar
                    </button>

                    <div class="float-end">
                        <button type="submit" name="formato" value="pdf" class="btn btn-danger">
                            <i class="fas fa-file-pdf"></i> Exportar PDF
                        </button>
                        <button type="submit" name="formato" value="csv" class="btn btn-success">
                            <i class="fas fa-file-csv"></i> Exportar CSV
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% if resultado %}
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    Molas Mais Vendidas - {{ periodo_texto }}
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Posição</th>
                                <th>Código</th>
                                <th>Cliente</th>
                                <th>Quantidade Vendida</th>
                                <th>Média Mensal</th>
                                <th>Variação %</th>
                                <th>Vs. Média Histórica</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for mola in resultado %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ mola.mola_codigo }}</td>
                                    <td>{{ mola.mola_cliente }}</td>
                                    <td>{{ mola.total_vendido }}</td>
                                    <td>{{ mola.media_mensal|default:"0" }}</td>
                                    <td>
                                        {% if mola.variacao_percentual > 0 %}
                                            <span class="text-success">+{{ mola.variacao_percentual }}%</span>
                                        {% elif mola.variacao_percentual < 0 %}
                                            <span class="text-danger">{{ mola.variacao_percentual }}%</span>
                                        {% else %}
                                            <span class="text-muted">0%</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if mola.variacao_media_historica > 0 %}
                                            <span class="text-success">+{{ mola.variacao_media_historica }}%</span>
                                        {% elif mola.variacao_media_historica < 0 %}
                                            <span class="text-danger">{{ mola.variacao_media_historica }}%</span>
                                        {% else %}
                                            <span class="text-muted">0%</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'mola-detail' mola.mola %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> Detalhes
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Gráfico de Tendência de Vendas -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Tendência de Vendas Totais por Mês</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="vendasMensaisChart"></canvas>
                </div>
                <hr>
                <div class="text-muted small">
                    Este gráfico mostra a tendência de vendas totais por mês no período selecionado.
                    A linha de tendência indica a direção geral das vendas.
                </div>
            </div>
        </div>
    {% elif request.method == 'POST' %}
        <div class="alert alert-info alert-dismissible fade show">
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
            Nenhuma mola encontrada com os filtros selecionados.
        </div>
    {% endif %}

    <!-- Script para gerar o gráfico de tendência -->
    {% if resultado %}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Obter dados de vendas mensais via AJAX
            fetch('{% url "vendas-mensais-json" %}?periodo={{ periodo }}{% if data_inicial %}&data_inicial={{ data_inicial|date:"Y-m-d" }}{% endif %}{% if data_final %}&data_final={{ data_final|date:"Y-m-d" }}{% endif %}')
                .then(response => response.json())
                .then(data => {
                    // Criar gráfico de linha
                    const ctx = document.getElementById('vendasMensaisChart').getContext('2d');
                    const chart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: data.labels,
                            datasets: [{
                                label: 'Vendas Mensais',
                                data: data.valores,
                                backgroundColor: 'rgba(187, 134, 252, 0.2)',
                                borderColor: '#bb86fc',
                                borderWidth: 2,
                                pointBackgroundColor: '#bb86fc',
                                pointBorderColor: '#fff',
                                pointRadius: 5,
                                pointHoverRadius: 7,
                                tension: 0.1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    grid: {
                                        color: 'rgba(255, 255, 255, 0.1)'
                                    },
                                    ticks: {
                                        color: 'rgba(255, 255, 255, 0.7)'
                                    }
                                },
                                x: {
                                    grid: {
                                        color: 'rgba(255, 255, 255, 0.1)'
                                    },
                                    ticks: {
                                        color: 'rgba(255, 255, 255, 0.7)'
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    labels: {
                                        color: 'rgba(255, 255, 255, 0.7)'
                                    }
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                    titleColor: '#fff',
                                    bodyColor: '#fff',
                                    borderColor: '#bb86fc',
                                    borderWidth: 1
                                }
                            }
                        }
                    });
                })
                .catch(error => {
                    console.error('Erro ao carregar dados de vendas mensais:', error);
                    document.getElementById('vendasMensaisChart').parentNode.innerHTML =
                        '<div class="alert alert-warning">Não foi possível carregar o gráfico de tendência.</div>';
                });
        });
    </script>
    {% endif %}
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const periodoSelect = document.getElementById('{{ form.periodo.id_for_label }}');
        const camposPersonalizados = document.querySelectorAll('.periodo-personalizado');
        const camposMesesEspecificos = document.querySelectorAll('.meses-especificos');
        const camposComparacao = document.querySelectorAll('.comparacao-periodos');

        // Função para mostrar/esconder campos de acordo com o período selecionado
        function toggleCampos() {
            const isPeriodoPersonalizado = periodoSelect.value === 'personalizado';
            const isMesesEspecificos = periodoSelect.value === 'meses_especificos';
            const isComparacao = periodoSelect.value === 'comparacao';

            camposPersonalizados.forEach(campo => {
                campo.style.display = isPeriodoPersonalizado ? 'block' : 'none';
            });

            camposMesesEspecificos.forEach(campo => {
                campo.style.display = isMesesEspecificos ? 'block' : 'none';
            });

            camposComparacao.forEach(campo => {
                campo.style.display = isComparacao ? 'block' : 'none';
            });
        }

        // Inicializar
        toggleCampos();

        // Adicionar evento de mudança
        periodoSelect.addEventListener('change', toggleCampos);

        // Controle dos seletores de mês/ano vs campos de data manual
        const checkboxSeletores = document.getElementById('usarSeletoresMesAno');
        const seletoresMesAno = document.querySelector('.seletores-mes-ano');
        const camposDataManual = document.querySelector('.campos-data-manual');

        function toggleSeletores() {
            if (checkboxSeletores.checked) {
                seletoresMesAno.style.display = 'block';
                camposDataManual.style.display = 'none';
            } else {
                seletoresMesAno.style.display = 'none';
                camposDataManual.style.display = 'block';
            }
        }

        // Inicializar
        toggleSeletores();

        // Adicionar evento de mudança
        checkboxSeletores.addEventListener('change', toggleSeletores);
    });
</script>
{% endblock %}
